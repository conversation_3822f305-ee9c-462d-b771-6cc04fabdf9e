<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="giant" nbSpinnerMessage="">
    <nb-card-header>
        <div class="header-actions">
            <button nbButton ghost size="small" (click)="manualRefresh()" [disabled]="loading">
                <nb-icon icon="refresh-outline"></nb-icon>
                Refresh
            </button>
        </div>
    </nb-card-header>
    <nb-card-body class="card-body">
        <ngx-error-message [error]="errorMessage"></ngx-error-message>
        <div class="table-header-wrapper">
            <div class="auto-refresh-indicator" *ngIf="refreshTimerSub">
                <nb-icon icon="sync-outline"></nb-icon>
                <span class="refresh-text">Auto-refresh: 10s</span>
            </div>
        </div>
        <ng2-smart-table *ngIf="datasource" [settings]="settings" [source]="datasource"
            [class.no-data]="summary.length <= 0" (userRowSelect)="viewJob($event)"></ng2-smart-table>
    </nb-card-body>
</nb-card>

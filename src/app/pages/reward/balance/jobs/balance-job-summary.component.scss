@import "../../../../@theme/styles/themes";

:host {
  .card-body {
    padding: 1.5rem;
  }

  .card-header {
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    justify-content: space-between;

    .title {
      i {
        margin-right: 5px;
      }
    }

    .filter {
      ::ng-deep label {
        cursor: pointer;
      }
    }
  }

  #filter {
    hr {
      border-top: 1px solid #151a30;
      margin-top: 1rem;
      margin-bottom: 1.5rem;
    }

    .suffix-button {
      border-radius: 0 0.25rem 0.25rem 0;
      padding: 11px 13px;
    }

    ::ng-deep nb-tooltip .content {
      font-weight: 800;
    }

    .actions {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-end;
      align-items: center;
      width: 100%;
      text-align: center;
      padding: 27px 0;

      button {
        margin-left: 8px;
      }
    }
  }

  .time {
    position: absolute;
    font-weight: 600;
    font-size: 14px;
    color: #8f9bb3;
  }

  ::ng-deep ngx-paged-result-data-header {
    font-size: 14px;
    font-weight: 600;
    color: nb-theme(color-basic-600);

    span {
      font-weight: 800;
      color: #fff;
    }
  }

  ::ng-deep ng2-smart-table {
    width: 100%;
    
    // Make the table responsive
    table {
      width: 100%;
      table-layout: auto;
      
      // Add horizontal scrolling for small screens
      @media (max-width: 768px) {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
      }
      
      // Change cursor to hand on hover
      tbody tr {
        cursor: pointer;
        
        &:hover {
          background-color: nb-theme(background-basic-hover-color);
        }
      }
    }
    
    // Style for the table headers
    thead tr th {
      font-weight: 800;
      font-size: 14px;
      padding: 0.6em !important;
      
      a, span {
        font-weight: 800;
        font-size: 14px;
      }
    }
    
    // Style for the table cells
    tbody tr td {
      font-size: 14px;
      padding: 0.6em;
      font-weight: 600;
      
      // Type column - allow wrapping and ellipsis for very long text
      &:first-child {
        max-width: 250px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        
        // On larger screens, allow more space
        @media (min-width: 992px) {
          max-width: 300px;
        }
        
        // On hover, show the full text
        &:hover {
          white-space: normal;
          overflow: visible;
          word-break: break-all;
        }
      }
      
      // Number columns - right align
      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4) {
        text-align: right;
      }

      // Badge styling for pending items
      .badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;

        &.warning {
          background-color: nb-theme(color-warning-100);
          color: nb-theme(color-warning-800);
          border: 1px solid nb-theme(color-warning-300);
        }

        &.basic {
          background-color: nb-theme(color-basic-100);
          color: nb-theme(color-basic-600);
          border: 1px solid nb-theme(color-basic-300);
        }

        &.info {
          background-color: nb-theme(color-info-100);
          color: nb-theme(color-info-800);
          border: 1px solid nb-theme(color-info-300);
        }

        nb-icon {
          font-size: 0.875rem;
        }
      }
    }
    
    // Style for no data
    &.no-data {
      thead th:last-child {
        display: none;
      }

      tbody td {
        padding: 0.8em;

        &:first-child {
          text-align: center;
          font-size: 14px;
          font-weight: 800;
          padding: 1.75em;
          color: nb-theme(color-basic-200);
        }
      }
    }
  }

  ::ng-deep nb-spinner {
    z-index: 1000;
  }

  .header-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    button {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      nb-icon {
        font-size: 1rem;
      }
    }
  }

  .table-header-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 1rem;
    min-height: 2rem;
  }

  .auto-refresh-indicator {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    white-space: nowrap;
    flex-shrink: 0;
    padding: 0.25rem 0;

    nb-icon {
      animation: spin 2s linear infinite;
      margin-right: 0.375rem;
      font-size: 0.875rem;
      color: nb-theme(color-info-500);
    }

    .refresh-text {
      font-weight: 400;
      color: nb-theme(color-basic-600);
      font-style: italic;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @media (max-width: 768px) {
    .table-header-wrapper {
      justify-content: center;

      .auto-refresh-indicator {
        font-size: 0.7rem;
      }
    }

    .header-actions {
      justify-content: center;

      button {
        font-size: 0.8rem;
      }
    }
  }
}

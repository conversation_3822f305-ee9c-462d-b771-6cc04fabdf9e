import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LocalDataSource } from 'ng2-smart-table';
import { NbDialogService } from '@nebular/theme';
import { RewardBalanceJobService, RewardBalanceJobSummary } from '../../../../@core/platform/reward';
import { RewardBalanceJobComponent } from './balance-job.component';

@Component({
    selector: 'ngx-reward-list',
    styleUrls: ['./balance-job-summary.component.scss'],
    templateUrl: './balance-job-summary.component.html',
})
export class RewardBalanceJobSummaryComponent implements OnInit {
    public loading: boolean = true;
    public datasource: LocalDataSource;
    public summary: RewardBalanceJobSummary[] = [];

    public settings = {
        actions: null,
        hideSubHeader: true,
        pager: {
            perPage: 10
        },
        columns: {
            type: {
                title: 'Type',
                type: 'string',
                width: '300px',
            },
            totalCount: {
                title: 'Total Count',
                type: 'number',
                width: '300px',
                sort: false
            },
            pendingCount: {
                title: 'Pending Items',
                type: 'number',
                width: '300px',
                sort: false
            },
            failedCount: {
                title: 'Failed Items',
                type: 'number',
                width: '300px',
                sort: false
            }
        }
    };

    constructor(
        protected readonly route: ActivatedRoute,
        protected readonly router: Router,
        private readonly dialog: NbDialogService,
        private readonly jobService: RewardBalanceJobService) {
    }

    public async ngOnInit(): Promise<void> {
        await this.load();
    }

    public async viewJob(event: any): Promise<void> {
        const modal = this.dialog.open(RewardBalanceJobComponent, {
            closeOnBackdropClick: false,
            context: {
                jobType: event.data.type,
            }
        });

        const ref = modal.onClose.subscribe(() => {
            ref.unsubscribe();
            this.load();
        });
    }

    private async load(): Promise<void> {
        this.loading = true;
        try {
            const summary = await this.jobService.getSummary().toPromise();
            this.datasource = new LocalDataSource(summary);
            this.summary = summary;
        } catch (error) {
            console.error('Failed to load job summary:', error);
        } finally {
            this.loading = false;
        }
    }
}

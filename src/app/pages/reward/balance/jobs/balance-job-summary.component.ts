import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LocalDataSource } from 'ng2-smart-table';
import { NbDialogService } from '@nebular/theme';
import { Subject, Subscription, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { RewardBalanceJobService, RewardBalanceJobSummary, RewardBalanceJobStatus, RewardBalanceJobPagedResult } from '../../../../@core/platform/reward';
import { RewardBalanceJobComponent } from './balance-job.component';

interface ExtendedRewardBalanceJobSummary extends RewardBalanceJobSummary {
    runningCount: number;
}

@Component({
    selector: 'ngx-reward-list',
    styleUrls: ['./balance-job-summary.component.scss'],
    templateUrl: './balance-job-summary.component.html',
})
export class RewardBalanceJobSummaryComponent implements OnInit, OnDestroy {
    public loading: boolean = true;
    public datasource: LocalDataSource;
    public summary: ExtendedRewardBalanceJobSummary[] = [];
    public refreshTimerSub: Subscription;
    public hasPendingJobs = false;
    public tableKey = 0;
    private destroyed = new Subject();
    private readonly REFRESH_INTERVAL = 10000;

    public settings = {
        actions: null,
        hideSubHeader: true,
        pager: {
            perPage: 10
        },
        columns: {
            type: {
                title: 'Type',
                type: 'string',
                width: '300px',
            },
            totalCount: {
                title: 'Total Count',
                type: 'number',
                width: '300px',
                sort: false
            },
            pendingCount: {
                title: 'Pending Items',
                type: 'number',
                width: '300px',
                sort: false
            },
            failedCount: {
                title: 'Failed Items',
                type: 'number',
                width: '300px',
                sort: false
            },
            runningCount: {
                title: 'Running',
                type: 'html',
                width: '300px',
                sort: false,
                valuePrepareFunction: (runningCount: number, row: ExtendedRewardBalanceJobSummary) => {
                    console.log(`valuePrepareFunction called for ${row.type} with count:`, runningCount);
                    const icon = runningCount > 0 ? `<span class="running-icon">▶</span>` : '';
                    const textClass = runningCount > 0 ? 'running-text' : '';
                    const result = `<span class="running-count ${textClass}">${icon}${runningCount}</span>`;
                    console.log(`valuePrepareFunction result:`, result);
                    return result;
                }
            }
        }
    };

    constructor(
        protected readonly route: ActivatedRoute,
        protected readonly router: Router,
        private readonly dialog: NbDialogService,
        private readonly jobService: RewardBalanceJobService) {
    }

    public async ngOnInit(): Promise<void> {
        await this.load();
        this.startRefreshTimer();
    }

    public ngOnDestroy(): void {
        this.destroyed.next();
        this.destroyed.complete();
        this.stopRefreshTimer();
    }

    public async viewJob(event: any): Promise<void> {
        const modal = this.dialog.open(RewardBalanceJobComponent, {
            closeOnBackdropClick: false,
            context: {
                jobType: event.data.type,
            }
        });

        const ref = modal.onClose.subscribe(() => {
            ref.unsubscribe();
            this.load();
        });
    }

    private async load(): Promise<void> {
        console.log('=== LOAD METHOD CALLED ===');
        this.loading = true;
        try {
            const summary = await this.jobService.getSummary().toPromise();
            console.log('Raw summary from API:', summary);

            const extendedSummary = await this.addRunningCounts(summary);
            console.log('Extended summary with running counts:', extendedSummary);

            // Force complete table re-render by incrementing key
            this.tableKey++;
            console.log('Table key incremented to:', this.tableKey);

            this.datasource = new LocalDataSource(extendedSummary);
            this.summary = extendedSummary;
            this.checkForPendingJobs();

            console.log('=== LOAD METHOD COMPLETED ===');
        } catch (error) {
            console.error('Failed to load job summary:', error);
        } finally {
            this.loading = false;
        }
    }

    private async addRunningCounts(summary: RewardBalanceJobSummary[]): Promise<ExtendedRewardBalanceJobSummary[]> {
        const runningCountPromises = summary.map(async (item) => {
            try {
                const runningJobs = await this.jobService.getAll({
                    type: item.type,
                    status: [RewardBalanceJobStatus.Running]
                }).toPromise();

                const runningCount = runningJobs.items.reduce((total: number, result: RewardBalanceJobPagedResult) => {
                    return total + (result.items ? result.items.length : 0);
                }, 0);

                console.log(`Running count for ${item.type}: ${runningCount}`);

                return {
                    ...item,
                    runningCount
                } as ExtendedRewardBalanceJobSummary;
            } catch (error) {
                console.error(`Failed to get running count for ${item.type}:`, error);
                return {
                    ...item,
                    runningCount: 0
                } as ExtendedRewardBalanceJobSummary;
            }
        });

        return Promise.all(runningCountPromises);
    }

    private checkForPendingJobs(): void {
        this.hasPendingJobs = this.summary.some(item => item.pendingCount > 0);
    }

    private startRefreshTimer(): void {
        this.stopRefreshTimer();

        this.refreshTimerSub = timer(this.REFRESH_INTERVAL, this.REFRESH_INTERVAL)
            .pipe(takeUntil(this.destroyed))
            .subscribe(() => this.load());
    }

    private stopRefreshTimer(): void {
        if (this.refreshTimerSub) {
            this.refreshTimerSub.unsubscribe();
            this.refreshTimerSub = null;
        }
    }

    public manualRefresh(): void {
        console.log('=== MANUAL REFRESH TRIGGERED ===');
        this.load();
    }
}

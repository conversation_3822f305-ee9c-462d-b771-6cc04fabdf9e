import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LocalDataSource } from 'ng2-smart-table';
import { NbDialogService } from '@nebular/theme';
import { Subject, Subscription, timer, forkJoin } from 'rxjs';
import { takeUntil, map } from 'rxjs/operators';
import { RewardBalanceJobService, RewardBalanceJobSummary, RewardBalanceJobStatus, RewardBalanceType, RewardBalanceJobPagedResult } from '../../../../@core/platform/reward';
import { RewardBalanceJobComponent } from './balance-job.component';

interface ExtendedRewardBalanceJobSummary extends RewardBalanceJobSummary {
    runningCount: number;
}

@Component({
    selector: 'ngx-reward-list',
    styleUrls: ['./balance-job-summary.component.scss'],
    templateUrl: './balance-job-summary.component.html',
})
export class RewardBalanceJobSummaryComponent implements OnInit, OnDestroy {
    public loading: boolean = true;
    public datasource: LocalDataSource;
    public summary: ExtendedRewardBalanceJobSummary[] = [];
    public refreshTimerSub: Subscription;
    public hasPendingJobs = false;
    private destroyed = new Subject();
    private readonly REFRESH_INTERVAL = 10000;

    public settings = {
        actions: null,
        hideSubHeader: true,
        pager: {
            perPage: 10
        },
        columns: {
            type: {
                title: 'Type',
                type: 'string',
                width: '300px',
            },
            totalCount: {
                title: 'Total Count',
                type: 'number',
                width: '300px',
                sort: false
            },
            pendingCount: {
                title: 'Pending Items',
                type: 'html',
                width: '300px',
                sort: false,
                valuePrepareFunction: (pendingCount: number) => {
                    const badgeClass = pendingCount > 0 ? 'warning' : 'basic';
                    const iconClass = pendingCount > 0 ? 'clock-outline' : '';
                    const icon = pendingCount > 0 ? `<nb-icon icon="${iconClass}"></nb-icon>` : '';
                    return `<span class="badge ${badgeClass}">${icon} ${pendingCount}</span>`;
                }
            },
            failedCount: {
                title: 'Failed Items',
                type: 'number',
                width: '300px',
                sort: false
            },
            runningCount: {
                title: 'Running',
                type: 'html',
                width: '300px',
                sort: false,
                valuePrepareFunction: (runningCount: number) => {
                    const badgeClass = runningCount > 0 ? 'info' : 'basic';
                    const iconClass = runningCount > 0 ? 'play-circle-outline' : '';
                    const icon = runningCount > 0 ? `<nb-icon icon="${iconClass}"></nb-icon>` : '';
                    return `<span class="badge ${badgeClass}">${icon} ${runningCount}</span>`;
                }
            }
        }
    };

    constructor(
        protected readonly route: ActivatedRoute,
        protected readonly router: Router,
        private readonly dialog: NbDialogService,
        private readonly jobService: RewardBalanceJobService) {
    }

    public async ngOnInit(): Promise<void> {
        await this.load();
        this.startRefreshTimer();
    }

    public ngOnDestroy(): void {
        this.destroyed.next();
        this.destroyed.complete();
        this.stopRefreshTimer();
    }

    public async viewJob(event: any): Promise<void> {
        const modal = this.dialog.open(RewardBalanceJobComponent, {
            closeOnBackdropClick: false,
            context: {
                jobType: event.data.type,
            }
        });

        const ref = modal.onClose.subscribe(() => {
            ref.unsubscribe();
            this.load();
        });
    }

    private async load(): Promise<void> {
        this.loading = true;
        try {
            const summary = await this.jobService.getSummary().toPromise();
            const extendedSummary = await this.addRunningCounts(summary);
            this.datasource = new LocalDataSource(extendedSummary);
            this.summary = extendedSummary;
            this.checkForPendingJobs();
        } catch (error) {
            console.error('Failed to load job summary:', error);
        } finally {
            this.loading = false;
        }
    }

    private async addRunningCounts(summary: RewardBalanceJobSummary[]): Promise<ExtendedRewardBalanceJobSummary[]> {
        const runningCountPromises = summary.map(async (item) => {
            try {
                const runningJobs = await this.jobService.getAll({
                    type: item.type,
                    status: [RewardBalanceJobStatus.Running],
                    page: 1,
                    pageSize: 1000
                }).toPromise();

                const runningCount = runningJobs.items.reduce((total: number, result: RewardBalanceJobPagedResult) => {
                    return total + (result.items ? result.items.length : 0);
                }, 0);

                return {
                    ...item,
                    runningCount
                } as ExtendedRewardBalanceJobSummary;
            } catch (error) {
                console.error(`Failed to get running count for ${item.type}:`, error);
                return {
                    ...item,
                    runningCount: 0
                } as ExtendedRewardBalanceJobSummary;
            }
        });

        return Promise.all(runningCountPromises);
    }

    private checkForPendingJobs(): void {
        this.hasPendingJobs = this.summary.some(item => item.pendingCount > 0);
    }

    private startRefreshTimer(): void {
        this.stopRefreshTimer();

        this.refreshTimerSub = timer(this.REFRESH_INTERVAL, this.REFRESH_INTERVAL)
            .pipe(takeUntil(this.destroyed))
            .subscribe(() => this.load());
    }

    private stopRefreshTimer(): void {
        if (this.refreshTimerSub) {
            this.refreshTimerSub.unsubscribe();
            this.refreshTimerSub = null;
        }
    }

    public manualRefresh(): void {
        this.load();
    }
}

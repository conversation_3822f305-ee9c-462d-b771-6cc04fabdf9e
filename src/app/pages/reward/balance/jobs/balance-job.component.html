<div id="reward-list" class="row">
  <div class="col-md-12">
    <nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="giant" nbSpinnerMessage="">
      <nb-card-header class="card-header">
        <span>
          Balance Jobs {{ jobType ? '- ' + jobType : '' }}
        </span>
        <div *ngIf="isModal" class="header-actions">
          <button nbButton ghost (click)="close()">
            <nb-icon icon="close-outline"></nb-icon>
          </button>
        </div>
      </nb-card-header>
      <nb-card-body class="card-body">
        <div id="filter">
          <div class="row">
            <div class="col-sm-3" *ngIf="!jobType">
              <div class="form-group">
                <label class="label" for="types">
                  Type
                </label>
                <nb-select name="types" multiple fullWidth placeholder="Any" [(ngModel)]="types" [disabled]="!!jobType">
                  <nb-option *ngFor="let type of types" [value]="type">{{ type }}</nb-option>
                </nb-select>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="label" for="statuses">
                  Status
                </label>
                <nb-select name="statuses" multiple fullWidth placeholder="Any" [(ngModel)]="statuses">
                  <nb-option value="Pending">Pending</nb-option>
                  <nb-option value="Running">Running</nb-option>
                  <nb-option value="Complete">Complete</nb-option>
                  <nb-option value="Failed">Failed</nb-option>
                </nb-select>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="label" for="startTime">
                  Start Date
                </label>
                <input nbInput fullWidth [nbDatepicker]="startDatePicker" placeholder="Start Date"
                  [(ngModel)]="startTime">
                <nb-datepicker #startDatePicker></nb-datepicker>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <div class="actions">
                <button nbButton status="primary" (click)="onFilterChange()">
                  <nb-icon icon="search-outline"></nb-icon>
                  Search
                </button>
                <button nbButton status="basic" (click)="onResetFilter()">
                  <nb-icon icon="refresh-outline"></nb-icon>
                  Reset
                </button>
                <button nbButton status="info" (click)="manualRefresh()" nbTooltip="Manually refresh job data">
                  <nb-icon icon="sync-outline"></nb-icon>
                  Refresh Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>
</div>
<div id="reward-result" class="row">
  <div class="col-md-12">
    <nb-card class="inline-form-card" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="giant"
      nbSpinnerMessage="">
      <nb-card-body class="card-body">
        <div class="auto-refresh-indicator" *ngIf="refreshTimerSub">
          <nb-icon icon="sync-outline" class="text-info me-1"></nb-icon>
          <small class="text-muted">Auto-refreshing every {{hasRunningJobs ? 5 : 3}}s</small>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <ngx-paged-result-data-header [page]="page" [pageCount]="pageCount" [totalCount]="totalCount"
            [loading]="loading"></ngx-paged-result-data-header>
        </div>
        <ng2-smart-table [settings]="settings" [source]="datasource"
          [class.no-data]="totalCount <= 0"></ng2-smart-table>
      </nb-card-body>
    </nb-card>
  </div>
</div>
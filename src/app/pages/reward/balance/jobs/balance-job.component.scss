@import "../../../../@theme/styles/themes";

:host {
  .card-body {
    padding: 1.5rem;
  }

  .card-header {
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    justify-content: space-between;

    .title {
      i {
        margin-right: 5px;
      }
    }

    .header-actions {
      button {
        padding: 0.5rem;
        margin-left: 0.5rem;
      }
    }
  }

  // Progress bar styles
  .progress-container {
    width: 100%;
    padding: 0.25rem 0;
    
    .progress {
      height: 1.25rem;
      background-color: nb-theme(background-basic-color-3);
      border-radius: 0.25rem;
      overflow: hidden;
      position: relative; // Add position relative
      
      .progress-bar {
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: white;
        text-align: center;
        white-space: nowrap;
        transition: width 0.6s ease;
        font-size: 0.75rem;
        font-weight: 600;
        height: 100%; // Ensure full height
        position: absolute; // Position absolute to prevent overflow
        left: 0; // Align to left
        top: 0; // Align to top
        
        &.primary {
          background-color: nb-theme(color-primary-default);
        }
        
        &.success {
          background-color: nb-theme(color-success-default);
        }
        
        &.info {
          background-color: nb-theme(color-info-default);
        }
        
        &.warning {
          background-color: nb-theme(color-warning-default);
          color: nb-theme(text-control-color);
        }
        
        &.danger {
          background-color: nb-theme(color-danger-default);
        }
        
        span {
          padding: 0 0.5rem;
          position: relative; // Make text position relative
          z-index: 1; // Ensure text is above the bar
          width: 100%; // Full width for text
          text-align: center; // Center text
        }

        // Animation for running jobs
        &.running {
          position: relative;
          overflow: hidden;
          animation: progressPulse 2s ease-in-out infinite;
          box-shadow: 0 0 8px rgba(52, 144, 220, 0.5);

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent,
              rgba(255, 255, 255, 0.4),
              transparent
            );
            animation: progressShimmer 1.5s infinite;
            z-index: 2;
          }

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              45deg,
              transparent 25%,
              rgba(255, 255, 255, 0.1) 25%,
              rgba(255, 255, 255, 0.1) 50%,
              transparent 50%,
              transparent 75%,
              rgba(255, 255, 255, 0.1) 75%
            );
            background-size: 20px 20px;
            animation: progressStripes 1s linear infinite;
            z-index: 1;
          }
        }
      }
    }
  }

  // Keyframe animations for running progress bars
  @keyframes progressShimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes progressPulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes progressStripes {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 20px 0;
    }
  }

  #filter {
    hr {
      border-top: 1px solid #151a30;
      margin-top: 1rem;
      margin-bottom: 1.5rem;
    }

    .suffix-button {
      border-radius: 0 0.25rem 0.25rem 0;
      padding: 11px 13px;
    }

    ::ng-deep nb-tooltip .content {
      font-weight: 800;
    }

    .actions {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-end;
      align-items: center;
      width: 100%;
      text-align: center;
      padding: 27px 0;

      button {
        margin-left: 8px;
      }
    }
  }

  .time {
    position: absolute;
    font-weight: 600;
    font-size: 14px;
    color: #8f9bb3;
  }

  ::ng-deep ngx-paged-result-data-header {
    font-size: 14px;
    font-weight: 600;
    color: nb-theme(color-basic-600);

    span {
      font-weight: 800;
      color: #fff;
    }
  }

  ::ng-deep ng2-smart-table {
    table {
      width: 100%;
      
      // Style for table headers
      thead tr th {
        padding: 0.75rem !important;
        font-weight: 600;
        vertical-align: middle;
        
        // Ensure header text doesn't wrap
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      // Style for table cells
      tbody tr td {
        padding: 0.5rem;
        vertical-align: middle;
      }
    }
    
    // Ensure the progress column has proper spacing
    .ng2-smart-th.progress, .ng2-smart-td.progress {
      min-width: 150px;
      max-width: 200px;
      padding: 0.5rem;
    }
  }

  ::ng-deep nb-spinner {
    z-index: 1000;
  }

  // Table header wrapper to properly align elements
  .table-header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    min-height: 2rem; // Ensure consistent height

    // Ensure the paged header takes priority space
    ngx-paged-result-data-header {
      flex: 1;
      min-width: 0; // Allow shrinking
      display: flex;
      align-items: center;
    }
  }

  // Auto-refresh indicator styling
  .auto-refresh-indicator {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    white-space: nowrap;
    flex-shrink: 0; // Prevent shrinking
    padding: 0.25rem 0;

    nb-icon {
      animation: spin 2s linear infinite;
      margin-right: 0.375rem;
      font-size: 0.875rem;
      color: nb-theme(color-info-500);
    }

    .refresh-text {
      font-weight: 400;
      color: nb-theme(color-basic-600);
      font-style: italic;
    }
  }

  // Spinning animation for the refresh icon
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  // Responsive behavior for smaller screens
  @media (max-width: 768px) {
    .table-header-wrapper {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;

      .auto-refresh-indicator {
        align-self: flex-end;
        font-size: 0.7rem;
      }
    }
  }
}

import { Component, OnD<PERSON>roy, OnInit, Input } from '@angular/core';
import { PagedResultDataBaseComponent, UrlParams } from '../../../../shared/components';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Observable, Subject, Subscription, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PagedResult } from '../../../../@core/data/paged-result';
import { PagedResultDataSourceSettings } from '../../../../@core/data/paged-result-data-source';
import { DateService } from '../../../../@core/services';
import { NbDialogRef } from '@nebular/theme';
import { DomSanitizer } from '@angular/platform-browser';
import {
  RewardBalanceType,
  RewardBalanceJobStatus,
  RewardBalanceJobFilter,
  RewardBalanceJobPagedResult,
  RewardBalanceJobService,
  RewardBalanceBatchJob
} from '../../../../@core/platform/reward';

@Component({
  selector: 'ngx-reward-list',
  styleUrls: ['./balance-job.component.scss'],
  templateUrl: './balance-job.component.html',
})
export class RewardBalanceJobComponent extends PagedResultDataBaseComponent<RewardBalanceJobPagedResult> implements OnInit, OnDestroy {
  private userSub: Subject<string> = new Subject();
  protected destroyed = new Subject();
  private refreshTimerSub: Subscription;
  private readonly REFRESH_INTERVAL = 5000; // 5 seconds
  
  // Flag to track if we have any running jobs
  private hasRunningJobs = false;

  @Input() jobType: RewardBalanceType;

  public statuses: RewardBalanceJobStatus[] = [];
  public types: RewardBalanceType[] = Object.values(RewardBalanceType);
  public startTime: Date;
  public endTime: Date;
  public isModal: boolean = false;

  public settings = {
    actions: null,
    hideSubHeader: true,
    pager: {
      perPage: 10
    },
    columns: {
      id: {
        title: 'ID',
        type: 'number',
      },
      type: {
        title: 'Source',
        type: 'string',
        sort: false
      },
      total: {
        title: 'Total',
        type: 'number',
        sort: false
      },
      pending: {
        title: 'Pending',
        type: 'number',
        sort: false
      },
      complete: {
        title: 'Complete',
        type: 'number',
        sort: false
      },
      failed: {
        title: 'Failed',
        type: 'number',
        sort: false
      },
      progressBar: {
        title: 'Progress',
        type: 'html',
        sort: false,
        valuePrepareFunction: (_: any, row: RewardBalanceBatchJob) => {
          let statusClass = '';

          switch (true) {
            case row.progress <= 40:
              statusClass = 'bg-danger';
              break;

            case row.progress > 40 && row.progress <= 80:
              statusClass = 'bg-warning';
              break;

            case row.progress > 80 && row.progress < 100:
              statusClass = 'bg-info';
              break;

            case row.progress === 100:
              statusClass = 'bg-success';
              break;

            default:
              statusClass = 'bg-primary';
              break;
          }

          // Add running animation class if the job is in running state
          const runningClass = row.status === RewardBalanceJobStatus.Running ? 'running' : '';

          return this.domSanitiser.bypassSecurityTrustHtml(`
            <div class="progress-container">
            <div class="progress">
                  <div class="progress-bar ${statusClass} ${runningClass}" style="width: ${row.progress}%">
                    <span>${row.progress}%</span>
                  </div>
                </div>
              </div>
          `);
        }
      },
      status: {
        title: 'Status',
        type: 'html',
        valuePrepareFunction: (status: RewardBalanceJobStatus) => {
          let badgeClass = '';
          switch (status) {
            case RewardBalanceJobStatus.Pending:
              badgeClass = 'warning';
              break;
            case RewardBalanceJobStatus.Running:
              badgeClass = 'info';
              break;
            case RewardBalanceJobStatus.Complete:
              badgeClass = 'success';
              break;
            case RewardBalanceJobStatus.Failed:
              badgeClass = 'danger';
              break;
            default:
              badgeClass = 'primary';
          }
          return `<span class="badge ${badgeClass}">${status}</span>`;
        },
      },
      restartCount: {
        title: 'Restarts',
        type: 'number',
      },
      startTime: {
        title: 'Start Time',
        type: 'Date',
        valuePrepareFunction: (date: Date) => this.dateService.transform(date)
      },
      endTime: {
        title: 'End Time',
        type: 'Date',
        sort: false,
        valuePrepareFunction: (date: Date) => this.dateService.transform(date)
      }
    }
  };

  constructor(
    protected readonly route: ActivatedRoute,
    protected readonly router: Router,
    private readonly dateService: DateService,
    private readonly jobService: RewardBalanceJobService,
    private readonly domSanitiser: DomSanitizer,
    private readonly dialogRef?: NbDialogRef<RewardBalanceJobComponent>,
  ) {
    super(route, router);
    this.isModal = !!this.dialogRef;
  }

  public ngOnInit(): void {
    super.ngOnInit();
    console.log('statuses', this.statuses);

    if (this.jobType) {
      this.types = [this.jobType];
      this.onFilterChange();
    }
    
    // Subscribe to data changes to check for running jobs
    this.datasource.onChanged().pipe(
      takeUntil(this.destroyed)
    ).subscribe(async () => {
      await this.checkForRunningJobs();
    });
  }

  public ngOnDestroy(): void {
    this.destroyed.next();
    this.destroyed.complete();
    this.userSub.unsubscribe();
    this.userSub = undefined;
    this.statuses = [];
    
    // Stop the refresh timer if it's running
    this.stopRefreshTimer();
    
    console.log('statuses on destroy', this.statuses);
  }

  protected load(settings: PagedResultDataSourceSettings): Observable<PagedResult<RewardBalanceJobPagedResult>> {
    const filter: RewardBalanceJobFilter = {
      page: settings.page,
      pageSize: settings.pageSize,
      type: this.jobType,
      status: this.statuses.length > 0 ? this.statuses : undefined,
      startTime: this.startTime,
      endTime: this.endTime,
    };

    return this.jobService.getAll(filter);
  }
  protected getUrlParams(page: number): Params {
    return {
      type: this.types.length || null,
      status: this.statuses.length > 0 ? this.statuses : null,
      dateFrom: this.dateService.urlFormat(this.startTime),
      dateTo: this.dateService.urlFormat(this.endTime),
      page: page || null,
      pageSize: this.pageSize || null
    };
  }

  protected onUrlParamChange(params: UrlParams): void {
    this.types = params.query?.type ? Array.isArray(params.query.type) ? params.query.type : [params.query.type] : [];
    this.startTime = params.query?.dateFrom ? new Date(params.query.dateFrom) : undefined;
    this.endTime = params.query?.dateTo ? new Date(params.query.dateTo) : undefined;

    this.datasource.setPage(this.page ?? 1, false);
    this.datasource.enabled = true;
  }

  public onResetFilter(): void {
    this.statuses = [];
    this.startTime = undefined;
    this.endTime = undefined;

    if (!this.isModal) {
      this.types = [];
    }

    this.onFilterChange();
  }

  public onFilterChange(): void {
    this.datasource.setPage(1);
  }

  public close(): void {
    if (this.dialogRef) {
      this.dialogRef.close(true);
      this.statuses = [];
    }
  }

  // Check if there are any running jobs in the current data
  private async checkForRunningJobs(): Promise<void> {
    if (!this.datasource) {
      return;
    }

    const data = await this.datasource.getElements();
    // Extract all jobs from the paged results and flatten them
    const allJobs: RewardBalanceBatchJob[] = [];
    data.forEach((result: RewardBalanceJobPagedResult) => {
      if (result.items) {
        allJobs.push(...result.items);
      }
    });

    const runningJobs = allJobs.filter((job: RewardBalanceBatchJob) => job.status === RewardBalanceJobStatus.Running);
    const hadRunningJobs = this.hasRunningJobs;
    this.hasRunningJobs = runningJobs.length > 0;

    // Start or stop the timer based on whether we have running jobs
    if (this.hasRunningJobs && !hadRunningJobs) {
      this.startRefreshTimer();
    } else if (!this.hasRunningJobs && hadRunningJobs) {
      this.stopRefreshTimer();
    }

    console.log(`Running jobs: ${runningJobs.length}`);
  }
  
  // Start the refresh timer
  private startRefreshTimer(): void {
    console.log('Starting refresh timer for running jobs');
    
    // Stop any existing timer first
    this.stopRefreshTimer();
    
    // Create a new timer that refreshes data every REFRESH_INTERVAL milliseconds
    this.refreshTimerSub = timer(this.REFRESH_INTERVAL, this.REFRESH_INTERVAL)
      .pipe(takeUntil(this.destroyed))
      .subscribe(() => {
        console.log('Auto-refreshing job data...');
        this.refreshData();
      });
  }
  
  // Stop the refresh timer
  private stopRefreshTimer(): void {
    if (this.refreshTimerSub) {
      this.refreshTimerSub.unsubscribe();
      this.refreshTimerSub = null;
      console.log('Stopped refresh timer');
    }
  }
  
  // Refresh the data without changing the page
  private refreshData(): void {
    if (this.datasource) {
      const currentPage = this.datasource.getPaging().page;
      this.datasource.refresh();
      console.log(`Refreshed data on page ${currentPage}`);
    }
  }
};
